<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto-Check on Countdown Expiry</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-button.danger:hover {
            background: #c82333;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .chatbot-container {
            border: 2px solid #007bff;
            border-radius: 8px;
            height: 400px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .countdown-display {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            margin: 10px 0;
        }
        .feature-highlight {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 Test Auto-Check on Countdown Expiry</h1>
        
        <div class="test-section">
            <h3>Feature Overview</h3>
            <div class="feature-highlight">
                <h4>🎯 What This Tests:</h4>
                <p>When the maintenance countdown reaches zero, the system automatically:</p>
                <ul>
                    <li><strong>Immediate Check:</strong> Checks maintenance status 2 seconds after expiry</li>
                    <li><strong>Periodic Check:</strong> Continues checking every 30 seconds until maintenance ends</li>
                    <li><strong>Auto-Hide:</strong> Hides maintenance screen when maintenance is no longer active</li>
                    <li><strong>Smart Cleanup:</strong> Stops periodic checking when maintenance ends</li>
                </ul>
            </div>
        </div>

        <div class="two-column">
            <div>
                <div class="test-section">
                    <h3>Test Controls</h3>
                    
                    <button class="test-button" onclick="initializeChatbot()" id="btnInit">Initialize Chatbot</button>
                    <button class="test-button" onclick="startShortCountdown()" id="btnShort">Start 15s Countdown</button>
                    <button class="test-button" onclick="startVeryShortCountdown()" id="btnVeryShort">Start 5s Countdown</button>
                    <button class="test-button danger" onclick="simulateMaintenanceEnd()" id="btnEnd">Simulate Maintenance End</button>
                    <button class="test-button" onclick="clearLogs()" id="btnClear">Clear Logs</button>
                    
                    <div class="countdown-display" id="countdownDisplay">
                        Countdown: Not Started
                    </div>
                </div>

                <div class="test-section">
                    <h3>Current Status</h3>
                    <div id="currentStatus" class="status info">
                        <div>Chatbot: <span id="chatbotStatus">Not Initialized</span></div>
                        <div>Maintenance Active: <span id="maintenanceStatus">Unknown</span></div>
                        <div>Countdown Active: <span id="countdownStatus">No</span></div>
                        <div>Auto-Check Active: <span id="autoCheckStatus">No</span></div>
                    </div>
                </div>

                <div class="test-section">
                    <h3>Activity Log</h3>
                    <div id="activityLog" class="log-container">
                        [System] Ready to test auto-check functionality...
                    </div>
                </div>
            </div>

            <div>
                <div class="test-section">
                    <h3>Maintenance UI</h3>
                    <div id="chatbotContainer" class="chatbot-container">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; text-align: center;">
                            <div>
                                <h3>🔧 Maintenance Screen</h3>
                                <p>Initialize chatbot and start countdown to see the maintenance UI</p>
                                <p><strong>Watch for:</strong></p>
                                <ul style="text-align: left; display: inline-block;">
                                    <li>Countdown timer</li>
                                    <li>Auto-check when expired</li>
                                    <li>Periodic checking</li>
                                    <li>Auto-hide when maintenance ends</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        let chatbot = null;
        let maintenanceStore = null;
        let countdownInterval = null;
        let testStartTime = null;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const typePrefix = type.toUpperCase().padEnd(7);
            logContainer.innerHTML += `\n[${timestamp}] ${typePrefix} ${message}`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus() {
            document.getElementById('chatbotStatus').textContent = chatbot ? 'Initialized' : 'Not Initialized';
            
            if (maintenanceStore) {
                document.getElementById('maintenanceStatus').textContent = 
                    maintenanceStore.isMaintenanceActive ? 'Yes' : 'No';
            }
            
            // Check if countdown is visible
            const countdownElement = document.querySelector('.countdown-container');
            document.getElementById('countdownStatus').textContent = 
                countdownElement ? 'Yes' : 'No';
                
            // Check if auto-check is active (look for console logs or timer activity)
            document.getElementById('autoCheckStatus').textContent = 
                maintenanceStore && maintenanceStore.maintenanceStatus && 
                maintenanceStore.maintenanceStatus.maintenance ? 'Possible' : 'No';
        }

        function updateCountdownDisplay(seconds) {
            const display = document.getElementById('countdownDisplay');
            if (seconds > 0) {
                display.textContent = `Countdown: ${seconds}s remaining`;
                display.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            } else {
                display.textContent = 'Countdown: EXPIRED - Auto-checking...';
                display.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            }
        }

        window.initializeChatbot = async function() {
            try {
                log('Initializing chatbot...', 'system');
                
                const chatbotModule = await import('http://localhost:5173/src/rag-chatbot-embed.ts');
                const maintenanceModule = await import('http://localhost:5173/src/stores/maintenance.ts');
                
                chatbot = new chatbotModule.LLMRagChatbot();
                await chatbot.initialize({
                    tenantId: 'test',
                    env: 'dev'
                });

                maintenanceStore = maintenanceModule.useMaintenanceStore();
                
                log('Chatbot initialized successfully', 'success');
                chatbot.open();
                
                updateStatus();
                
            } catch (error) {
                log(`Initialization failed: ${error.message}`, 'error');
                console.error('Initialization error:', error);
            }
        };

        function startCountdownTest(seconds, label) {
            if (!maintenanceStore) {
                log('Maintenance store not available!', 'error');
                return;
            }

            log(`Starting ${label} countdown test...`, 'system');
            testStartTime = Date.now();

            // Set mock maintenance data with short countdown
            const mockData = {
                maintenance: true,
                message: `テスト用メンテナンス（${seconds}秒後に自動チェック）`,
                start_date: new Date(Date.now() - 60000).toISOString(),
                end_date: new Date(Date.now() + seconds * 1000).toISOString()
            };

            maintenanceStore.maintenanceStatus = mockData;
            log(`Mock maintenance set: expires in ${seconds} seconds`, 'info');

            // Start countdown display
            let remaining = seconds;
            updateCountdownDisplay(remaining);
            
            countdownInterval = setInterval(() => {
                remaining--;
                updateCountdownDisplay(remaining);
                
                if (remaining <= 0) {
                    clearInterval(countdownInterval);
                    log('Countdown expired! Watch for auto-check...', 'warning');
                }
            }, 1000);

            updateStatus();
        }

        window.startShortCountdown = function() {
            startCountdownTest(15, '15-second');
        };

        window.startVeryShortCountdown = function() {
            startCountdownTest(5, '5-second');
        };

        window.simulateMaintenanceEnd = function() {
            if (!maintenanceStore) {
                log('Maintenance store not available!', 'error');
                return;
            }

            log('Simulating maintenance end...', 'system');
            
            // Set maintenance to false to simulate end
            const mockData = {
                maintenance: false,
                message: 'メンテナンスが終了しました',
                start_date: new Date(Date.now() - 3600000).toISOString(),
                end_date: new Date(Date.now() - 60000).toISOString()
            };

            maintenanceStore.maintenanceStatus = mockData;
            log('Maintenance status set to inactive', 'success');
            
            updateStatus();
        };

        window.clearLogs = function() {
            document.getElementById('activityLog').innerHTML = '[System] Logs cleared...';
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            document.getElementById('countdownDisplay').textContent = 'Countdown: Not Started';
            document.getElementById('countdownDisplay').style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        };

        // Auto-update status every 2 seconds
        setInterval(updateStatus, 2000);

        log('Auto-check test page loaded. Initialize chatbot to begin.', 'system');
        log('Expected behavior: Countdown → Expiry → Auto-check → Periodic checks → Auto-hide when maintenance ends', 'info');
    </script>
</body>
</html>
