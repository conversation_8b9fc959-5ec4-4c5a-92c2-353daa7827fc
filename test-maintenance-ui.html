<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Maintenance UI Features</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .chatbot-container {
            border: 2px solid #007bff;
            border-radius: 8px;
            height: 600px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Maintenance UI Features</h1>
        
        <div class="test-section">
            <h3>New Features Overview</h3>
            <div class="feature-list">
                <h4>✨ Added Features:</h4>
                <ul>
                    <li><strong>Close Button:</strong> Top-right corner close button (like ChatBotHeader)</li>
                    <li><strong>Countdown Timer:</strong> Real-time countdown to maintenance end_date</li>
                    <li><strong>Auto-refresh:</strong> Countdown updates every second</li>
                    <li><strong>Responsive Design:</strong> Works on mobile and desktop</li>
                    <li><strong>Dark Mode Support:</strong> Styled for both light and dark themes</li>
                </ul>
            </div>
        </div>

        <div class="two-column">
            <div>
                <div class="test-section">
                    <h3>Test Controls</h3>
                    
                    <div class="input-group">
                        <label for="tenantId">Tenant ID:</label>
                        <input type="text" id="tenantId" placeholder="Enter tenant ID" value="test">
                    </div>
                    
                    <div class="input-group">
                        <label for="testMode">Test Mode:</label>
                        <select id="testMode">
                            <option value="real">Real API</option>
                            <option value="mock-active">Mock: Active Maintenance</option>
                            <option value="mock-countdown">Mock: Countdown (5 min)</option>
                            <option value="mock-expired">Mock: Expired Maintenance</option>
                        </select>
                    </div>
                    
                    <button class="test-button" onclick="initializeChatbot()" id="btnInit">Initialize Chatbot</button>
                    <button class="test-button" onclick="simulateMaintenance()" id="btnSimulate">Simulate Maintenance</button>
                    <button class="test-button" onclick="testCloseButton()" id="btnClose">Test Close Button</button>
                    <button class="test-button" onclick="toggleDarkMode()" id="btnDark">Toggle Dark Mode</button>
                    <button class="test-button" onclick="clearLogs()" id="btnClear">Clear Logs</button>
                </div>

                <div class="test-section">
                    <h3>Current Status</h3>
                    <div id="currentStatus" class="status info">
                        <div>Chatbot Status: <span id="chatbotStatus">Not Initialized</span></div>
                        <div>Maintenance Active: <span id="maintenanceStatus">Unknown</span></div>
                        <div>Countdown Active: <span id="countdownStatus">No</span></div>
                        <div>Chat Open: <span id="chatOpenStatus">No</span></div>
                    </div>
                </div>

                <div class="test-section">
                    <h3>Activity Log</h3>
                    <div id="activityLog" class="log-container">
                        [System] Ready to test maintenance UI features...
                    </div>
                </div>
            </div>

            <div>
                <div class="test-section">
                    <h3>Maintenance UI Preview</h3>
                    <div id="chatbotContainer" class="chatbot-container">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; text-align: center;">
                            <div>
                                <h3>Maintenance UI will appear here</h3>
                                <p>Initialize chatbot and simulate maintenance to see the UI</p>
                                <p><strong>Features to test:</strong></p>
                                <ul style="text-align: left; display: inline-block;">
                                    <li>Close button (top-right)</li>
                                    <li>Countdown timer</li>
                                    <li>Auto-refresh status</li>
                                    <li>Dark mode styling</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        let chatbot = null;
        let maintenanceStore = null;
        let isDarkMode = false;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const typePrefix = type.toUpperCase().padEnd(7);
            logContainer.innerHTML += `\n[${timestamp}] ${typePrefix} ${message}`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus() {
            document.getElementById('chatbotStatus').textContent = chatbot ? 'Initialized' : 'Not Initialized';
            
            if (maintenanceStore) {
                document.getElementById('maintenanceStatus').textContent = 
                    maintenanceStore.isMaintenanceActive ? 'Yes' : 'No';
            }
            
            if (chatbot) {
                document.getElementById('chatOpenStatus').textContent = 
                    chatbot.isOpen() ? 'Yes' : 'No';
            }
            
            // Check if countdown is visible
            const countdownElement = document.querySelector('.countdown-container');
            document.getElementById('countdownStatus').textContent = 
                countdownElement ? 'Yes' : 'No';
        }

        window.initializeChatbot = async function() {
            try {
                log('Initializing chatbot...', 'system');
                
                const chatbotModule = await import('http://localhost:5173/src/rag-chatbot-embed.ts');
                const maintenanceModule = await import('http://localhost:5173/src/stores/maintenance.ts');
                
                chatbot = new chatbotModule.LLMRagChatbot();
                const tenantId = document.getElementById('tenantId').value || 'test';
                
                await chatbot.initialize({
                    tenantId: tenantId,
                    env: 'dev'
                });

                maintenanceStore = maintenanceModule.useMaintenanceStore();
                
                log(`Chatbot initialized with tenant: ${tenantId}`, 'success');
                chatbot.open();
                
                updateStatus();
                
            } catch (error) {
                log(`Initialization failed: ${error.message}`, 'error');
                console.error('Initialization error:', error);
            }
        };

        window.simulateMaintenance = async function() {
            if (!maintenanceStore) {
                log('Maintenance store not available!', 'error');
                return;
            }

            const testMode = document.getElementById('testMode').value;
            log(`Simulating maintenance mode: ${testMode}`, 'system');

            try {
                let mockData = null;
                
                switch (testMode) {
                    case 'mock-active':
                        mockData = {
                            maintenance: true,
                            message: 'システムメンテナンス中です。しばらくお待ちください。',
                            start_date: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
                            end_date: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
                        };
                        break;
                    case 'mock-countdown':
                        mockData = {
                            maintenance: true,
                            message: 'メンテナンス終了まであと少しです。',
                            start_date: new Date(Date.now() - 60000).toISOString(),
                            end_date: new Date(Date.now() + 300000).toISOString() // 5 minutes from now
                        };
                        break;
                    case 'mock-expired':
                        mockData = {
                            maintenance: true,
                            message: 'メンテナンス終了予定時刻を過ぎました。',
                            start_date: new Date(Date.now() - 3600000).toISOString(),
                            end_date: new Date(Date.now() - 60000).toISOString() // 1 minute ago
                        };
                        break;
                    default:
                        await maintenanceStore.getMaintenanceStatus();
                        log('Using real API response', 'info');
                        updateStatus();
                        return;
                }

                // Mock the maintenance status
                maintenanceStore.maintenanceStatus = mockData;
                log(`Mock maintenance data set: ${JSON.stringify(mockData)}`, 'success');
                
                updateStatus();
                
            } catch (error) {
                log(`Simulation failed: ${error.message}`, 'error');
            }
        };

        window.testCloseButton = function() {
            if (!chatbot) {
                log('Chatbot not initialized!', 'error');
                return;
            }

            log('Testing close button functionality...', 'system');
            
            // The close button should close the chatbot
            const closeButton = document.querySelector('.close-icon');
            if (closeButton) {
                log('Close button found, simulating click...', 'info');
                closeButton.click();
                
                setTimeout(() => {
                    updateStatus();
                    log('Close button test completed', 'success');
                }, 500);
            } else {
                log('Close button not found - maintenance screen may not be active', 'warning');
            }
        };

        window.toggleDarkMode = function() {
            isDarkMode = !isDarkMode;
            const chatbotContainer = document.getElementById('chatbotContainer');
            
            if (isDarkMode) {
                chatbotContainer.classList.add('dark');
                document.getElementById('btnDark').textContent = 'Light Mode';
                log('Dark mode enabled', 'info');
            } else {
                chatbotContainer.classList.remove('dark');
                document.getElementById('btnDark').textContent = 'Toggle Dark Mode';
                log('Light mode enabled', 'info');
            }
        };

        window.clearLogs = function() {
            document.getElementById('activityLog').innerHTML = '[System] Logs cleared...';
        };

        // Auto-update status every 2 seconds
        setInterval(updateStatus, 2000);

        log('Test page loaded. Click "Initialize Chatbot" to begin testing.', 'system');
    </script>
</body>
</html>
