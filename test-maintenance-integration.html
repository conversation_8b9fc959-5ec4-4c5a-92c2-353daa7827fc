<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Maintenance Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .chatbot-container {
            border: 2px solid #007bff;
            border-radius: 8px;
            height: 500px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Maintenance Integration</h1>
        
        <div class="test-section">
            <h3>Integration Overview</h3>
            <p>This test demonstrates the automatic maintenance checking integration:</p>
            <ul>
                <li><strong>Automatic Check:</strong> Every RAG service API call triggers a maintenance check (max once per minute)</li>
                <li><strong>UI Integration:</strong> Maintenance component is displayed in ChatBot when maintenance is active</li>
                <li><strong>Smart Caching:</strong> Maintenance status is cached for 1 minute to avoid excessive API calls</li>
            </ul>
            <div class="status info">
                <strong>Note:</strong> Make sure the chatbot is initialized and the maintenance service is configured properly.
            </div>
        </div>

        <div class="two-column">
            <div>
                <div class="test-section">
                    <h3>Test Controls</h3>
                    <div class="input-group">
                        <label for="tenantId">Tenant ID (optional):</label>
                        <input type="text" id="tenantId" placeholder="Enter tenant ID to test">
                    </div>
                    
                    <button class="test-button" onclick="initializeChatbot()" id="btnInit">Initialize Chatbot</button>
                    <button class="test-button" onclick="simulateRagCall()" id="btnRag">Simulate RAG API Call</button>
                    <button class="test-button" onclick="forceMaintenanceCheck()" id="btnMaintenance">Force Maintenance Check</button>
                    <button class="test-button" onclick="toggleMaintenanceMode()" id="btnToggle">Toggle Test Mode</button>
                    <button class="test-button" onclick="clearLogs()" id="btnClear">Clear Logs</button>
                </div>

                <div class="test-section">
                    <h3>Current Status</h3>
                    <div id="currentStatus" class="result-box">
                        <div>Chatbot Initialized: <span id="chatbotStatus">No</span></div>
                        <div>Maintenance Active: <span id="maintenanceStatus">Unknown</span></div>
                        <div>Last Check: <span id="lastCheck">Never</span></div>
                        <div>API Calls Made: <span id="apiCallCount">0</span></div>
                    </div>
                </div>

                <div class="test-section">
                    <h3>Activity Log</h3>
                    <div id="activityLog" class="log-container">
                        [System] Waiting for initialization...
                    </div>
                </div>
            </div>

            <div>
                <div class="test-section">
                    <h3>Chatbot Preview</h3>
                    <div id="chatbotContainer" class="chatbot-container">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            Chatbot will appear here after initialization
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        let chatbot = null;
        let maintenanceStore = null;
        let apiCallCount = 0;
        let isTestMode = false;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const typePrefix = type.toUpperCase().padEnd(7);
            logContainer.innerHTML += `\n[${timestamp}] ${typePrefix} ${message}`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus() {
            document.getElementById('chatbotStatus').textContent = chatbot ? 'Yes' : 'No';
            document.getElementById('apiCallCount').textContent = apiCallCount;
            
            if (maintenanceStore) {
                document.getElementById('maintenanceStatus').textContent = 
                    maintenanceStore.isMaintenanceActive ? 'Yes' : 'No';
                document.getElementById('lastCheck').textContent = 
                    maintenanceStore.maintenanceStatus ? 'Recently' : 'Never';
            }
        }

        window.initializeChatbot = async function() {
            try {
                log('Initializing chatbot...', 'system');
                
                // Import modules
                const chatbotModule = await import('http://localhost:5173/src/rag-chatbot-embed.ts');
                const maintenanceModule = await import('http://localhost:5173/src/stores/maintenance.ts');
                
                // Initialize chatbot
                chatbot = new chatbotModule.LLMRagChatbot();
                const tenantId = document.getElementById('tenantId').value || 'test';
                
                await chatbot.initialize({
                    tenantId: tenantId,
                    env: 'dev'
                });

                // Get maintenance store
                maintenanceStore = maintenanceModule.useMaintenanceStore();
                
                log(`Chatbot initialized with tenant: ${tenantId}`, 'success');
                
                // Open chatbot in the preview container
                chatbot.open();
                
                updateStatus();
                
            } catch (error) {
                log(`Initialization failed: ${error.message}`, 'error');
                console.error('Initialization error:', error);
            }
        };

        window.simulateRagCall = async function() {
            if (!chatbot) {
                log('Chatbot not initialized!', 'error');
                return;
            }

            try {
                apiCallCount++;
                log(`Making RAG API call #${apiCallCount}...`, 'api');
                
                // This should trigger the maintenance check automatically
                await chatbot.sendMessage('Hello, this is a test message');
                
                log(`RAG API call #${apiCallCount} completed`, 'success');
                updateStatus();
                
            } catch (error) {
                log(`RAG API call failed: ${error.message}`, 'error');
            }
        };

        window.forceMaintenanceCheck = async function() {
            if (!maintenanceStore) {
                log('Maintenance store not available!', 'error');
                return;
            }

            try {
                log('Forcing maintenance status check...', 'system');
                
                const tenantId = document.getElementById('tenantId').value || undefined;
                await maintenanceStore.getMaintenanceStatus(tenantId);
                
                log(`Maintenance check completed. Status: ${maintenanceStore.isMaintenanceActive ? 'ACTIVE' : 'INACTIVE'}`, 'success');
                
                if (maintenanceStore.maintenanceMessage) {
                    log(`Maintenance message: ${maintenanceStore.maintenanceMessage}`, 'info');
                }
                
                updateStatus();
                
            } catch (error) {
                log(`Maintenance check failed: ${error.message}`, 'error');
            }
        };

        window.toggleMaintenanceMode = function() {
            isTestMode = !isTestMode;
            const button = document.getElementById('btnToggle');
            
            if (isTestMode) {
                button.textContent = 'Exit Test Mode';
                button.style.background = '#dc3545';
                log('Test mode enabled - simulating maintenance responses', 'warning');
            } else {
                button.textContent = 'Toggle Test Mode';
                button.style.background = '#007bff';
                log('Test mode disabled - using real API responses', 'info');
            }
        };

        window.clearLogs = function() {
            document.getElementById('activityLog').innerHTML = '[System] Logs cleared...';
            apiCallCount = 0;
            updateStatus();
        };

        // Auto-update status every 5 seconds
        setInterval(updateStatus, 5000);

        // Initial log
        log('Test page loaded. Click "Initialize Chatbot" to begin.', 'system');
    </script>
</body>
</html>
