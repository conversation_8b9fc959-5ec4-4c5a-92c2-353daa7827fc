<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Maintenance Store</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f0f0f0;
      }
      .test-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-section {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      .test-button:hover {
        background: #0056b3;
      }
      .test-button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .status {
        margin-top: 10px;
        padding: 10px;
        border-radius: 5px;
        font-weight: bold;
      }
      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .status.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      .input-group {
        margin: 10px 0;
      }
      .input-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      .input-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
      }
      .result-box {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
        font-family: monospace;
        white-space: pre-wrap;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>Test Maintenance Store</h1>

      <div class="test-section">
        <h3>Maintenance Service Configuration</h3>
        <p>
          <strong>Base URL:</strong> Configured via
          <code>VITE_MAINTENANCE_SERVICE_BASE_URL</code> environment variable
        </p>
        <p>
          <strong>Default URL:</strong> https://0p02gbetp3.execute-api.ap-northeast-1.amazonaws.com
        </p>
        <p><strong>Endpoint:</strong> GET /tenants/{tenant_id}</p>
        <div class="status info">
          <strong>Note:</strong> This test requires the chatbot to be initialized and a valid tenant
          ID. Make sure the environment variable is set in your .env file.
        </div>
      </div>

      <div class="test-section">
        <h3>Test Configuration</h3>
        <div class="input-group">
          <label for="tenantId">Tenant ID (optional - will use auth store if empty):</label>
          <input type="text" id="tenantId" placeholder="Enter tenant ID to test" />
        </div>
      </div>

      <div class="test-section">
        <h3>Test 1: Get Maintenance Status</h3>
        <p>Fetch the complete maintenance status for the specified tenant.</p>
        <button class="test-button" onclick="testGetMaintenanceStatus()" id="btn1">
          Get Maintenance Status
        </button>
        <div id="result1" class="result-box" style="display: none"></div>
      </div>

      <div class="test-section">
        <h3>Test 2: Quick Maintenance Check</h3>
        <p>Get a simple boolean result indicating if maintenance is active.</p>
        <button class="test-button" onclick="testQuickCheck()" id="btn2">Quick Check</button>
        <div id="result2" class="result-box" style="display: none"></div>
      </div>

      <div class="test-section">
        <h3>Test 3: Store Getters</h3>
        <p>Test the reactive getters after fetching maintenance status.</p>
        <button class="test-button" onclick="testGetters()" id="btn3">Test Getters</button>
        <div id="result3" class="result-box" style="display: none"></div>
      </div>

      <div class="test-section">
        <h3>Test 4: Error Handling</h3>
        <p>Test with an invalid tenant ID to see error handling.</p>
        <button class="test-button" onclick="testErrorHandling()" id="btn4">
          Test Error Handling
        </button>
        <div id="result4" class="result-box" style="display: none"></div>
      </div>

      <div class="test-section">
        <h3>Current Store State</h3>
        <button class="test-button" onclick="showCurrentState()">Show Current State</button>
        <div id="currentState" class="result-box" style="display: none"></div>
      </div>
    </div>

    <script type="module">
      let maintenanceStore = null
      let chatbot = null

      // Initialize
      async function initialize() {
        try {
          // Import the chatbot and maintenance store
          const chatbotModule = await import('http://localhost:3000/src/rag-chatbot-embed.ts')
          const maintenanceModule = await import('http://localhost:3000/src/stores/maintenance.ts')

          chatbot = new chatbotModule.LLMRagChatbot()
          await chatbot.initialize({
            tenantId: 'test',
            env: 'dev',
          })

          // Get the maintenance store instance
          maintenanceStore = maintenanceModule.useMaintenanceStore()

          console.log('Initialization successful')
          updateButtonStates(false)
        } catch (error) {
          console.error('Initialization failed:', error)
          showError('Failed to initialize. Make sure the dev server is running on localhost:3000')
        }
      }

      function updateButtonStates(disabled) {
        ;['btn1', 'btn2', 'btn3', 'btn4'].forEach((id) => {
          document.getElementById(id).disabled = disabled
        })
      }

      function showResult(elementId, content, type = 'success') {
        const element = document.getElementById(elementId)
        element.textContent = content
        element.style.display = 'block'
        element.className = `result-box ${type}`
      }

      function showError(message) {
        document.body.insertAdjacentHTML(
          'afterbegin',
          `<div class="status error" style="margin-bottom: 20px;">${message}</div>`,
        )
      }

      window.testGetMaintenanceStatus = async function () {
        if (!maintenanceStore) {
          showResult('result1', 'Maintenance store not initialized', 'error')
          return
        }

        try {
          updateButtonStates(true)
          const tenantId = document.getElementById('tenantId').value || undefined

          showResult('result1', 'Loading...', 'info')

          const result = await maintenanceStore.getMaintenanceStatus(tenantId)

          const output = {
            success: !!result,
            data: result,
            errors: maintenanceStore.errors,
            loadings: maintenanceStore.loadings,
          }

          showResult('result1', JSON.stringify(output, null, 2))
        } catch (error) {
          showResult('result1', `Error: ${error.message}`, 'error')
        } finally {
          updateButtonStates(false)
        }
      }

      window.testQuickCheck = async function () {
        if (!maintenanceStore) {
          showResult('result2', 'Maintenance store not initialized', 'error')
          return
        }

        try {
          updateButtonStates(true)
          const tenantId = document.getElementById('tenantId').value || undefined

          showResult('result2', 'Loading...', 'info')

          const result = await maintenanceStore.checkMaintenance(tenantId)

          const output = {
            isMaintenanceActive: result,
            timestamp: new Date().toISOString(),
          }

          showResult('result2', JSON.stringify(output, null, 2))
        } catch (error) {
          showResult('result2', `Error: ${error.message}`, 'error')
        } finally {
          updateButtonStates(false)
        }
      }

      window.testGetters = function () {
        if (!maintenanceStore) {
          showResult('result3', 'Maintenance store not initialized', 'error')
          return
        }

        const getters = {
          isUnderMaintenance: maintenanceStore.isUnderMaintenance,
          maintenanceMessage: maintenanceStore.maintenanceMessage,
          maintenanceStartDate: maintenanceStore.maintenanceStartDate,
          maintenanceEndDate: maintenanceStore.maintenanceEndDate,
          isMaintenanceActive: maintenanceStore.isMaintenanceActive,
        }

        showResult('result3', JSON.stringify(getters, null, 2))
      }

      window.testErrorHandling = async function () {
        if (!maintenanceStore) {
          showResult('result4', 'Maintenance store not initialized', 'error')
          return
        }

        try {
          updateButtonStates(true)

          showResult('result4', 'Testing with invalid tenant ID...', 'info')

          const result = await maintenanceStore.getMaintenanceStatus('invalid-tenant-id-12345')

          const output = {
            result: result,
            errors: maintenanceStore.errors,
            note: 'Check if error was handled gracefully',
          }

          showResult('result4', JSON.stringify(output, null, 2))
        } catch (error) {
          showResult('result4', `Unexpected error: ${error.message}`, 'error')
        } finally {
          updateButtonStates(false)
        }
      }

      window.showCurrentState = function () {
        if (!maintenanceStore) {
          showResult('currentState', 'Maintenance store not initialized', 'error')
          return
        }

        const state = {
          maintenanceStatus: maintenanceStore.maintenanceStatus,
          loadings: maintenanceStore.loadings,
          errors: maintenanceStore.errors,
          getters: {
            isUnderMaintenance: maintenanceStore.isUnderMaintenance,
            maintenanceMessage: maintenanceStore.maintenanceMessage,
            maintenanceStartDate: maintenanceStore.maintenanceStartDate,
            maintenanceEndDate: maintenanceStore.maintenanceEndDate,
            isMaintenanceActive: maintenanceStore.isMaintenanceActive,
          },
        }

        showResult('currentState', JSON.stringify(state, null, 2))
      }

      // Initialize when page loads
      initialize()
    </script>
  </body>
</html>
