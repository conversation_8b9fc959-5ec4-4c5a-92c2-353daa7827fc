import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
let isRefreshingToken = false
let requestsPending = [] as any[]

const createAxios = (baseURL: string) => {
  const newInstance = axios.create({ baseURL })
  newInstance.interceptors.request.use((config: any) => {
    const authStore = useAuthStore()
    const accessToken = authStore.chatbot_token
    const refreshToken = authStore.chatbot_refresh_token
    if (['/login/refresh'].includes(config.url) && refreshToken) {
      config.headers['Authorization'] = 'Bearer ' + refreshToken
    } else if (accessToken) {
      config.headers['Authorization'] = 'Bearer ' + accessToken
    }
    return config
  })

  /**
    * @description if any of the API gets 401 status code, this method
    calls getAuthToken method to renew accessToken
    * updates the error configuration and retries all failed requests
    again
  */
  newInstance.interceptors.response.use(
    (config) => {
      return { ...config, body: config.data }
    },
    async (error) => {
      const _errorResponse = error.response
      const originalRequest = error.config
      const status = _errorResponse.status
      const authStore = useAuthStore()
      // check it is login/refresh request, logout
      if (['/login/refresh'].includes(originalRequest.url)) {
        return Promise.reject(error)
      }
      // const { authService } = _useAPI()

      switch (status) {
        case 401:
          if (['/login/refresh'].includes(originalRequest.url)) {
            const settingsStore = useSettingsStore()
            await settingsStore.logout()
            return Promise.reject(error)
          } else {
            // retry with refrest token first
            if (!originalRequest._retry) {
              // save request if it is not refresh token request
              if (isRefreshingToken) {
                return new Promise(function (resolve) {
                  requestsPending.push(function () {
                    resolve(newInstance(originalRequest))
                  })
                })
              }
              originalRequest._retry = true
              isRefreshingToken = true

              try {
                const refreshTokenRes = await authStore.refreshToken()

                if (refreshTokenRes) {
                  originalRequest.headers['Authorization'] = 'Bearer ' + refreshTokenRes.token
                  return newInstance(originalRequest)
                } else {
                  const settingsStore = useSettingsStore()
                  await settingsStore.logout()
                }
              } catch (refreshError) {
                isRefreshingToken = false
                const settingsStore = useSettingsStore()
                await settingsStore.logout()
                return Promise.reject(refreshError)
              } finally {
                isRefreshingToken = false
                requestsPending.forEach((callback) => callback())
                requestsPending = []
              }
            } else {
              const settingsStore = useSettingsStore()
              await settingsStore.logout()
            }
          }

          const settingsStore = useSettingsStore()
          settingsStore.logout()
          break
        case 404:
          break

        default:
          break
      }

      return Promise.reject(error)
    },
  )

  return newInstance
}

const _useAPI = () => {
  const ragServiceBaseUrl = import.meta.env.VITE_RAG_SERVICE_BASE_URL
  const ragService = createAxios(ragServiceBaseUrl)

  const maintenanceServiceBaseUrl = import.meta.env.VITE_MAINTENANCE_SERVICE_BASE_URL
  const maintenanceService = createAxios(maintenanceServiceBaseUrl)

  return {
    ragService,
    maintenanceService,
  }
}

export const useAPI = _useAPI
