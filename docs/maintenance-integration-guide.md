# Maintenance Integration Guide

## Overview

The maintenance system is now fully integrated into the chatbot with automatic checking and UI display capabilities.

## Key Features

### 1. Automatic Maintenance Checking

- **Trigger**: Every RAG service API call automatically checks maintenance status
- **Throttling**: Maximum one check per minute to prevent excessive API calls
- **Smart Caching**: Results are cached for 1 minute
- **Exclusion**: Maintenance service calls don't trigger additional checks

### 2. UI Integration

- **Automatic Display**: Maintenance screen appears when maintenance is active
- **Priority**: Maintenance screen has higher priority than normal chat interface
- **Close Button**: Top-right close button to close chatbot (like Cha<PERSON><PERSON>otHeader)
- **Countdown Timer**: Real-time countdown to maintenance end_date
- **Auto-refresh**: Component automatically refreshes status every 30 seconds
- **Manual Retry**: Users can manually check status with a button

### 3. Seamless User Experience

- **Non-blocking**: Maintenance checks don't block normal API calls
- **Graceful Fallback**: Failed maintenance checks don't affect chat functionality
- **Visual Feedback**: Clear maintenance message with schedule information

## Implementation

### Files Modified/Created

1. **`src/composables/useAPI.ts`**

   - Added maintenance check logic to RAG service interceptor
   - Implemented throttling mechanism (1-minute intervals)

2. **`src/components/ChatBotMaintenance.vue`**

   - New component for displaying maintenance status
   - Auto-refresh functionality
   - Japanese UI with schedule display

3. **`src/components/ChatBot.vue`**

   - Integrated maintenance component
   - Added maintenance state checking

4. **`src/stores/maintenance.ts`**
   - Maintenance store with full API integration
   - Reactive getters for UI components

## Testing

### Test Files

1. **`test-maintenance-integration.html`**

   - Complete integration testing interface
   - Simulates RAG API calls
   - Real-time status monitoring
   - Activity logging

2. **`test-maintenance.html`**
   - Basic maintenance store testing
   - API endpoint testing
   - Error handling verification

### How to Test

1. **Start Development Server**

   ```bash
   npm run dev
   ```

2. **Open Test Files**

   - Open `test-maintenance-integration.html` in browser
   - Click "Initialize Chatbot"
   - Use "Simulate RAG API Call" to trigger maintenance checks

3. **Verify Behavior**
   - Check activity logs for maintenance check triggers
   - Verify 1-minute throttling works correctly
   - Test maintenance UI display when active

## Configuration

### Environment Variables

```env
# Required: Maintenance service URL
VITE_MAINTENANCE_SERVICE_BASE_URL=https://0p02gbetp3.execute-api.ap-northeast-1.amazonaws.com

# Required: RAG service URL
VITE_RAG_SERVICE_BASE_URL=https://your-rag-service-url.amazonaws.com
```

### Customization

#### Maintenance Check Interval

```typescript
// In src/composables/useAPI.ts
const MAINTENANCE_CHECK_INTERVAL = 60 * 1000 // Change to desired interval
```

#### Auto-refresh Interval

```vue
<!-- In ChatBotMaintenance.vue -->
<ChatBotMaintenance :auto-refresh="true" :refresh-interval="30000" :show-schedule="true" />
```

#### Countdown Timer Features

- **Real-time Updates**: Updates every second when end_date is available
- **Smart Display**: Shows days, hours, minutes, seconds as needed
- **Expired State**: Shows special message when maintenance time has passed
- **Auto-check on Expiry**: Automatically checks maintenance status when countdown reaches zero
- **Periodic Check**: Checks every 30 seconds after countdown expires until maintenance ends
- **Auto-cleanup**: Automatically stops when component is unmounted
- **Close Button**: Top-right close button to close chatbot (same as ChatBotHeader)
- **LIFF Mode Support**: Close button automatically hidden when liffId is present

## API Integration

### Maintenance Check Flow

1. **User Action**: User sends message or performs action
2. **API Interceptor**: RAG service interceptor checks if maintenance check needed
3. **Throttling Check**: Verifies last check was > 1 minute ago
4. **Maintenance API**: Calls `GET /tenants/{tenant_id}` if needed
5. **Store Update**: Updates maintenance store with latest status
6. **UI Update**: ChatBot component reactively shows maintenance screen if active

### Error Handling

- **Network Errors**: Gracefully handled, don't block chat functionality
- **Invalid Responses**: Logged as warnings, assume no maintenance
- **Authentication Errors**: Use same auth flow as other services

## Troubleshooting

### Common Issues

1. **Maintenance not showing**

   - Check environment variables are set
   - Verify tenant ID is correct
   - Check browser console for errors

2. **Too many API calls**

   - Verify throttling is working (check network tab)
   - Ensure MAINTENANCE_CHECK_INTERVAL is set correctly

3. **UI not updating**
   - Check if maintenance store is properly imported
   - Verify reactive references are used correctly

### Debug Mode

Enable debug logging by adding to browser console:

```javascript
localStorage.setItem('debug', 'maintenance:*')
```

## Production Considerations

1. **Performance**: Maintenance checks are throttled and cached
2. **Reliability**: Failed checks don't affect core functionality
3. **User Experience**: Clear messaging and auto-refresh capabilities
4. **Monitoring**: All maintenance checks are logged for debugging

## Future Enhancements

- **WebSocket Integration**: Real-time maintenance status updates
- **Scheduled Maintenance**: Proactive notifications before maintenance
- **Custom Messages**: Tenant-specific maintenance messages
- **Maintenance History**: Track maintenance events over time
