# Maintenance Store Documentation

## Overview

The Maintenance Store provides functionality to check the maintenance status of specific tenants using the maintenance service API.

## API Endpoint

- **Base URL**: `https://0p02gbetp3.execute-api.ap-northeast-1.amazonaws.com`
- **Endpoint**: `GET /tenants/{tenant_id}`

## Response Format

```typescript
interface MaintenanceStatus {
  maintenance: boolean
  message: string
  start_date: string  // ISO 8601 format
  end_date: string    // ISO 8601 format
}
```

## Store Structure

### State

```typescript
{
  maintenanceStatus: MaintenanceStatus | null,
  loadings: Record<string, boolean>,
  errors: Record<string, any>
}
```

### Getters

- **`isUnderMaintenance`**: Returns boolean indicating if maintenance flag is true
- **`maintenanceMessage`**: Returns the maintenance message string
- **`maintenanceStartDate`**: Returns the maintenance start date
- **`maintenanceEndDate`**: Returns the maintenance end date
- **`isMaintenanceActive`**: Returns boolean indicating if maintenance is currently active based on dates and flag

### Actions

- **`getMaintenanceStatus(tenantId?: string)`**: Fetches maintenance status for a tenant
- **`checkMaintenance(tenantId?: string)`**: Quick boolean check for maintenance status
- **`clearMaintenanceStatus()`**: Clears the current maintenance status
- **`reset()`**: Resets the entire store state

## Usage Examples

### Basic Usage

```typescript
import { useMaintenanceStore } from '@/stores/maintenance'

const maintenanceStore = useMaintenanceStore()

// Check maintenance status
const status = await maintenanceStore.getMaintenanceStatus('tenant123')

// Quick boolean check
const isInMaintenance = await maintenanceStore.checkMaintenance('tenant123')

// Access reactive state
const { isUnderMaintenance, maintenanceMessage } = storeToRefs(maintenanceStore)
```

### In Vue Component

```vue
<template>
  <div v-if="isMaintenanceActive" class="maintenance-banner">
    <h3>System Under Maintenance</h3>
    <p>{{ maintenanceMessage }}</p>
    <p v-if="maintenanceEndDate">
      Expected to resume: {{ new Date(maintenanceEndDate).toLocaleString() }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useMaintenanceStore } from '@/stores/maintenance'

const maintenanceStore = useMaintenanceStore()
const { 
  isMaintenanceActive, 
  maintenanceMessage, 
  maintenanceEndDate 
} = storeToRefs(maintenanceStore)

onMounted(async () => {
  await maintenanceStore.getMaintenanceStatus()
})
</script>
```

### Error Handling

```typescript
const maintenanceStore = useMaintenanceStore()

try {
  const status = await maintenanceStore.getMaintenanceStatus('tenant123')
  if (status) {
    console.log('Maintenance status:', status)
  } else {
    console.log('Failed to get maintenance status')
  }
} catch (error) {
  console.error('Error checking maintenance:', error)
}

// Or check errors from store
const { errors } = storeToRefs(maintenanceStore)
if (errors.value.getMaintenanceStatus) {
  console.error('Maintenance check failed:', errors.value.getMaintenanceStatus)
}
```

### Integration with Auth Store

The maintenance store automatically uses the tenant ID from the auth store if no tenant ID is provided:

```typescript
import { useAuthStore } from '@/stores/auth'
import { useMaintenanceStore } from '@/stores/maintenance'

const authStore = useAuthStore()
const maintenanceStore = useMaintenanceStore()

// This will use authStore.tenantId automatically
await maintenanceStore.getMaintenanceStatus()

// Or explicitly provide a different tenant ID
await maintenanceStore.getMaintenanceStatus('different-tenant')
```

### Periodic Checks

```typescript
import { useMaintenanceStore } from '@/stores/maintenance'

const maintenanceStore = useMaintenanceStore()

// Check maintenance status every 5 minutes
setInterval(async () => {
  await maintenanceStore.checkMaintenance()
}, 5 * 60 * 1000)
```

## Maintenance Logic

The store provides two levels of maintenance checking:

1. **`isUnderMaintenance`**: Simply returns the `maintenance` flag from the API
2. **`isMaintenanceActive`**: Checks both the flag AND the current time against start/end dates

### Date-based Logic

- If no dates are provided, `isMaintenanceActive` returns the same as `isUnderMaintenance`
- If dates are provided, it checks if the current time is within the maintenance window
- Start date is inclusive (maintenance starts at this time)
- End date is inclusive (maintenance ends at this time)

## Example API Response

```json
{
  "maintenance": true,
  "message": "Scheduled maintenance for system upgrades",
  "start_date": "2024-01-15T02:00:00Z",
  "end_date": "2024-01-15T06:00:00Z"
}
```

## Testing

Use the example component at `src/examples/maintenance-example.vue` to test the maintenance store functionality:

1. Import the component in your app
2. Enter a tenant ID (or leave blank to use auth store tenant ID)
3. Click "Check Maintenance Status" to fetch data
4. Use "Quick Check" for boolean result only
5. View raw API response and formatted status

## Notes

- The maintenance service uses the same authentication as other services
- Failed requests return `null` instead of throwing errors for graceful handling
- The store automatically clears errors when new requests are made
- All dates should be in ISO 8601 format for proper parsing
